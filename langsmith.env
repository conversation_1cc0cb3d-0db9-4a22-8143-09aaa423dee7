# LangSmith Configuration for GitLab Development Environment
# Source this file to enable <PERSON><PERSON><PERSON> tracing across all services
# Usage: source langsmith.env

# LangSmith API Configuration
export LANGSMITH_API_KEY="***************************************************"
export LANGSMITH_PROJECT="rathid-gdk"
export LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
export LANGSMITH_TRACING="true"

# Legacy LangChain environment variables (for backward compatibility)
export LANGCHAIN_TRACING_V2="true"
export LANGCHAIN_API_KEY="***************************************************"
export LANGCHAIN_PROJECT="rathid-gdk"
export LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"

# OpenTelemetry configuration for <PERSON><PERSON><PERSON>
export OTEL_ENABLED="true"
export OTEL_EXPORTER_OTLP_ENDPOINT="https://api.smith.langchain.com/otel"
export OTEL_EXPORTER_OTLP_HEADERS="x-api-key=***************************************************,Langsmith-Project=rathid-gdk"
export OTEL_SERVICE_NAME="gitlab-gdk"

# LangSmith specific settings
export LANGSMITH_HIDE_INPUTS="false"
export LANGSMITH_HIDE_OUTPUTS="false"
export LANGSMITH_TRACING_SAMPLING_RATE="1.0"

# Background callbacks for better performance in development
export LANGCHAIN_CALLBACKS_BACKGROUND="true"

echo "✅ LangSmith environment configured:"
echo "   Project: $LANGSMITH_PROJECT"
echo "   Endpoint: $LANGSMITH_ENDPOINT"
echo "   Tracing: $LANGSMITH_TRACING"
echo ""
echo "🔍 To verify configuration, check: https://smith.langchain.com"
