#!/bin/bash

# LangSmith Setup Script for GitLab Development Kit
# This script helps enable/disable <PERSON><PERSON><PERSON> tracing across all GDK services

set -e

LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="rathid-gdk"
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  GitLab LangSmith Configuration Tool  ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_langsmith_status() {
    echo "Checking current LangSmith configuration..."
    echo ""
    
    # Check AI Gateway
    if grep -q "LANGSMITH_TRACING=true" gitlab-ai-gateway/.env 2>/dev/null; then
        print_success "AI Gateway: LangSmith enabled"
    else
        print_warning "AI Gateway: LangSmith not enabled"
    fi
    
    # Check env.runit
    if grep -q "LANGSMITH_TRACING=" env.runit 2>/dev/null; then
        print_success "GDK Services: LangSmith configured"
    else
        print_warning "GDK Services: LangSmith not configured"
    fi
    
    # Check mise.local.toml
    if grep -q "LANGSMITH_API_KEY" mise.local.toml 2>/dev/null; then
        print_success "Rails Application: LangSmith configured"
    else
        print_warning "Rails Application: LangSmith not configured"
    fi
    
    echo ""
}

enable_langsmith() {
    print_info "Enabling LangSmith tracing across all services..."
    echo ""
    
    # Source the centralized configuration
    if [ -f "langsmith.env" ]; then
        source langsmith.env
        print_success "Loaded LangSmith environment configuration"
    else
        print_error "langsmith.env file not found!"
        exit 1
    fi
    
    # Apply mise configuration
    if command -v mise &> /dev/null; then
        mise set
        print_success "Applied mise environment configuration"
    else
        print_warning "mise not found, skipping mise configuration"
    fi
    
    print_success "LangSmith tracing enabled!"
    echo ""
    print_info "Project: $LANGSMITH_PROJECT"
    print_info "Endpoint: $LANGSMITH_ENDPOINT"
    echo ""
    print_info "To verify traces are being sent, visit: https://smith.langchain.com"
}

disable_langsmith() {
    print_info "Disabling LangSmith tracing..."
    echo ""
    
    # Update AI Gateway .env
    if [ -f "gitlab-ai-gateway/.env" ]; then
        sed -i.bak 's/LANGSMITH_TRACING=true/LANGSMITH_TRACING=false/g' gitlab-ai-gateway/.env
        sed -i.bak 's/LANGCHAIN_TRACING_V2=true/LANGCHAIN_TRACING_V2=false/g' gitlab-ai-gateway/.env
        print_success "Disabled LangSmith in AI Gateway"
    fi
    
    # Update env.runit
    if [ -f "env.runit" ]; then
        sed -i.bak 's/LANGSMITH_TRACING="true"/LANGSMITH_TRACING="false"/g' env.runit
        sed -i.bak 's/LANGCHAIN_TRACING_V2="true"/LANGCHAIN_TRACING_V2="false"/g' env.runit
        print_success "Disabled LangSmith in GDK services"
    fi
    
    print_success "LangSmith tracing disabled!"
}

restart_services() {
    print_info "Restarting GDK services to apply changes..."
    echo ""
    
    if command -v gdk &> /dev/null; then
        gdk restart
        print_success "GDK services restarted"
    else
        print_warning "gdk command not found, please restart services manually"
    fi
}

show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  enable     Enable LangSmith tracing across all services"
    echo "  disable    Disable LangSmith tracing across all services"
    echo "  status     Check current LangSmith configuration status"
    echo "  restart    Restart GDK services"
    echo "  help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 enable    # Enable LangSmith tracing"
    echo "  $0 status    # Check current status"
    echo "  $0 disable   # Disable LangSmith tracing"
}

main() {
    print_header
    
    case "${1:-help}" in
        "enable")
            enable_langsmith
            echo ""
            print_info "Run '$0 restart' to restart services and apply changes"
            ;;
        "disable")
            disable_langsmith
            echo ""
            print_info "Run '$0 restart' to restart services and apply changes"
            ;;
        "status")
            check_langsmith_status
            ;;
        "restart")
            restart_services
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

main "$@"
