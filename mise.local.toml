# Local environment configuration for GitLab Development Kit
# This file is not committed to git and contains local development settings

[env]
# Lang<PERSON>mith Configuration for GitLab Rails Application
LANGSMITH_API_KEY = "***************************************************"
LANGSMITH_PROJECT = "rathid-gdk"
LANGSMITH_ENDPOINT = "https://api.smith.langchain.com"
LANGSMITH_TRACING = "true"

# Legacy LangChain environment variables (for backward compatibility)
LANGCHAIN_TRACING_V2 = "true"
LANGCHAIN_API_KEY = "***************************************************"
LANGCHAIN_PROJECT = "rathid-gdk"
LANGCHAIN_ENDPOINT = "https://api.smith.langchain.com"

# OpenTelemetry configuration for Lang<PERSON><PERSON>
OTEL_ENABLED = "true"
OTEL_EXPORTER_OTLP_ENDPOINT = "https://api.smith.langchain.com/otel"
OTEL_EXPORTER_OTLP_HEADERS = "x-api-key=***************************************************,Langsmith-Project=rathid-gdk"
OTEL_SERVICE_NAME = "gitlab-rails"

# LangSmith specific settings
********************* = "false"
LANGSMITH_HIDE_OUTPUTS = "false"
LANGSMITH_TRACING_SAMPLING_RATE = "1.0"
LANGCHAIN_CALLBACKS_BACKGROUND = "true"
