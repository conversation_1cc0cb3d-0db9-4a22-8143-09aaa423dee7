# LangSmith Tracing Setup for GitLab Development Kit

This guide explains how to use Lang<PERSON>mith tracing across your entire GitLab development environment, including GDK, AI Gateway, Duo Workflow Service, and VS Code extension.

## 🎯 Overview

LangSmith tracing has been configured across all GitLab development services to send traces to your dedicated project: **`rathid-gdk`**

### Services Configured:
- ✅ **AI Gateway** - Python FastAPI service
- ✅ **Duo Workflow Service** - Python gRPC service  
- ✅ **GitLab Rails Application** - Ruby on Rails
- ✅ **VS Code Extension** - TypeScript/Node.js
- ✅ **All GDK Services** - Via environment variables

## 🚀 Quick Start

### 1. Check Current Status
```bash
./setup-langsmith.sh status
```

### 2. Enable LangSmith (if not already enabled)
```bash
./setup-langsmith.sh enable
```

### 3. Restart Services
```bash
./setup-langsmith.sh restart
# OR manually:
gdk restart
```

### 4. Verify Traces
Visit [Lang<PERSON>mith Dashboard](https://smith.langchain.com) and look for the **`rathid-gdk`** project.

## 📁 Configuration Files

### Core Configuration Files:
- **`langsmith.env`** - Centralized environment configuration
- **`setup-langsmith.sh`** - Management script
- **`mise.local.toml`** - Rails application environment
- **`env.runit`** - GDK services environment
- **`gitlab-ai-gateway/.env`** - AI Gateway & Duo Workflow Service
- **`.vscode/launch.json`** - VS Code extension debugging

### Environment Variables Set:
```bash
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="rathid-gdk"
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_TRACING="true"

# Legacy compatibility
LANGCHAIN_TRACING_V2="true"
LANGCHAIN_API_KEY="***************************************************"
LANGCHAIN_PROJECT="rathid-gdk"

# OpenTelemetry support
OTEL_ENABLED="true"
OTEL_EXPORTER_OTLP_ENDPOINT="https://api.smith.langchain.com/otel"
OTEL_EXPORTER_OTLP_HEADERS="x-api-key=...,Langsmith-Project=rathid-gdk"
```

## 🔧 Management Commands

### Setup Script Usage:
```bash
# Check configuration status
./setup-langsmith.sh status

# Enable tracing
./setup-langsmith.sh enable

# Disable tracing  
./setup-langsmith.sh disable

# Restart services
./setup-langsmith.sh restart

# Show help
./setup-langsmith.sh help
```

## 🐛 Debugging GitLab DAP Flow

### Complete End-to-End Tracing:
1. **Start monitoring** (from gitlab-vscode-extension directory):
   ```bash
   ./test-gitlab-extension.sh start
   ```

2. **Launch VS Code extension** with LangSmith tracing:
   - Press `F5` in VS Code
   - Select "Debug Extension with Duo Platform"
   - LangSmith environment variables are automatically loaded

3. **Monitor traces** in real-time:
   - Visit [LangSmith Dashboard](https://smith.langchain.com)
   - Select the **`rathid-gdk`** project
   - Watch traces appear as you use Duo features

### Trace Flow:
```
VS Code Extension → GitLab Rails → AI Gateway → Duo Workflow Service → LLM Providers
        ↓              ↓              ↓                ↓                    ↓
    LangSmith      LangSmith      LangSmith        LangSmith           LangSmith
```

## 📊 What Gets Traced

### AI Gateway:
- Code completion requests
- Chat interactions
- Model provider calls (Anthropic, OpenAI, etc.)
- Request/response logging

### Duo Workflow Service:
- Workflow executions
- Agent interactions
- Tool usage
- LangGraph state transitions

### GitLab Rails:
- Duo feature usage
- API calls to AI Gateway
- User interactions

### VS Code Extension:
- Extension activation
- Duo platform communications
- User commands and interactions

## 🔍 Monitoring & Verification

### Check Service Logs:
```bash
# AI Gateway logs
tail -f log/gitlab-ai-gateway/gateway_debug.log

# Duo Workflow Service logs  
tail -f log/duo-workflow-service/service.log

# GitLab Rails logs
tail -f log/rails-web/development.log
```

### Verify Environment Variables:
```bash
# Check if variables are loaded
env | grep LANGSMITH
env | grep LANGCHAIN
```

### Test LangSmith Connection:
```bash
# Source the configuration
source langsmith.env

# Verify connection (if you have langsmith CLI)
langsmith --help
```

## 🛠️ Troubleshooting

### Common Issues:

1. **No traces appearing**:
   - Check `./setup-langsmith.sh status`
   - Verify services are restarted after configuration
   - Check service logs for errors

2. **Wrong project in LangSmith**:
   - Verify `LANGSMITH_PROJECT="rathid-gdk"` in all config files
   - Restart all services

3. **API key issues**:
   - Verify API key is correct in all configuration files
   - Check LangSmith dashboard for API key validity

4. **VS Code extension not tracing**:
   - Ensure you're using "Debug Extension with Duo Platform" configuration
   - Check VS Code developer console for errors

### Reset Configuration:
```bash
# Disable and re-enable
./setup-langsmith.sh disable
./setup-langsmith.sh enable
./setup-langsmith.sh restart
```

## 🔗 Useful Links

- [LangSmith Dashboard](https://smith.langchain.com)
- [LangSmith Documentation](https://docs.smith.langchain.com)
- [GitLab DAP Debugging Guide](./GITLAB_DAP_DEBUGGING_GUIDE.md)
- [VS Code Extension Testing](./test-gitlab-extension.sh)

## 📝 Notes

- All configuration files are set up for development use
- API key is embedded in configuration files (not recommended for production)
- Traces are sent to the `rathid-gdk` project as requested
- Full sampling rate (1.0) is enabled for comprehensive tracing
- Background callbacks are enabled for better performance
